@import 'tailwindcss';
@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/typography';

/* Global styles */
h1, h2, h3, h4, h5, h6, ul, ol, li, blockquote, figure, hr {
    margin: 0;
    padding: 0;
}

html, body {
	height: 100%;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Custom background pattern for hero section */
.hero-bg {
	background-image: 
		linear-gradient(45deg, rgba(0,0,0,0.1) 25%, transparent 25%), 
		linear-gradient(-45deg, rgba(0,0,0,0.1) 25%, transparent 25%), 
		linear-gradient(45deg, transparent 75%, rgba(0,0,0,0.1) 75%), 
		linear-gradient(-45deg, transparent 75%, rgba(0,0,0,0.1) 75%);
	background-size: 20px 20px;
	background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

/* Smooth scrolling */
html {
	scroll-behavior: smooth;
}

/* Custom animations */
@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(30px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.animate-fade-in-up {
	animation: fadeInUp 1s ease-out;
}

/* Hamburger menu animations */
.hamburger-line {
	transition: all 0.3s ease;
	transform-origin: center;
}

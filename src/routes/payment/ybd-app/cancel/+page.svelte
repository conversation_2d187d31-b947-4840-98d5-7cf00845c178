<script lang="ts">
	import { onMount } from 'svelte';

	let showCloseMessage = false;
	let isNativeApp = false;

	onMount(() => {
		// Check if running in Capacitor native app
		isNativeApp = !!(window as any).Capacitor ||
		              navigator.userAgent.includes('CapacitorWebView') ||
		              (window as any).webkit?.messageHandlers;

		if (isNativeApp) {
			// Native app - redirect using custom URL scheme
			// no session ID on cancel, but you could read other params if you like
			const target = `yourbestdays://payment/ybd-app/cancel`;
			window.location.replace(target);
		}
	});

	function closeWindow() {
		showCloseMessage = true;
	}
</script>

<svelte:head>
	<title>Payment Cancelled - Your Best Days App</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center p-8 bg-gray-100">
	<div class="bg-white rounded-xl max-w-lg w-full text-center shadow-lg flex flex-col">
		<div class="p-8 md:p-12 pb-4 md:pb-6 flex-grow">

			{#if isNativeApp}
				<!-- Native app redirect message -->
				<h1 class="text-gray-800 text-3xl md:text-4xl font-bold flex items-center justify-center gap-3">
					<svg class="w-8 h-8 md:w-10 md:h-10 text-red-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
						<circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
						<path d="M15 9l-6 6M9 9l6 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
					</svg>
					Payment Cancelled
				</h1>

				<p class="text-gray-600 text-lg mt-8 leading-relaxed" style="text-align:center; padding:2rem;">
					Redirecting back to the app…
				</p>
			{:else}
				<!-- Web browser message -->
				<h1 class="text-gray-800 text-3xl md:text-4xl font-bold flex items-center justify-center gap-3">
					<svg class="w-8 h-8 md:w-10 md:h-10 text-red-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
						<circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
						<path d="M15 9l-6 6M9 9l6 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
					</svg>
					Payment Cancelled
				</h1>

				<p class="text-gray-600 text-lg mt-8 leading-relaxed">
					Your payment has been cancelled. No charges have been made to your account.
				</p>

				<div class="bg-gray-50 rounded-lg p-6 mb-2">
					<p class="text-gray-700 mb-2">If there was a mistake or you would like to try again, you can return to the <span class="font-bold">Your Best Days</span> app payment page and try again. Need help? Contact our support team for assistance.</p>
				</div>

				<div class="flex gap-4 justify-center flex-wrap">
					<a href="/contact" class="inline-block px-6 py-3 rounded-md font-medium transition-all duration-200 bg-gray-200 text-gray-700 hover:bg-gray-300">
						Contact Support
					</a>
				</div>
			{/if}
		</div>
		
		<div class="bg-gray-50 rounded-b-xl py-3 px-8">
			<p class="text-xs text-gray-600">
				Your Best Days - brought to you by UpliftingActions LLC
			</p>
		</div>
	</div>
</div>

<style>
	/* Mobile responsive adjustments */
	@media (max-width: 640px) {
		.flex-wrap a {
			@apply w-full text-center;
		}
	}
</style>
<script lang="ts">
	import { onMount } from 'svelte';

	let showCloseMessage = false;
	let isNativeApp = false;

	onMount(() => {
		// Check if running in Capacitor native app
		isNativeApp = !!(window as any).Capacitor ||
		              navigator.userAgent.includes('CapacitorWebView') ||
		              (window as any).webkit?.messageHandlers;

		if (isNativeApp) {
			// Native app - redirect using custom URL scheme
			const params = new URLSearchParams(window.location.search);
			const sessionId = params.get('session_id');
			const target = `yourbestdays://payment/ybd-app/success?session_id=${sessionId}`;
			window.location.replace(target);
		}
	});

	function closeWindow() {
		showCloseMessage = true;
	}
</script>

<svelte:head>
	<title>Payment Successful - Your Best Days App</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center p-8 bg-gray-100">
	<div class="bg-white rounded-xl max-w-lg w-full text-center shadow-lg flex flex-col">
		<div class="p-8 md:p-12 pb-4 md:pb-6 flex-grow">
			<div class="mb-8">
				<img src="/ybd_icon-2.svg" alt="YBD App" class="w-24 h-24 mx-auto" />
			</div>

		{#if isNativeApp}
			<!-- Native app redirect message -->
			<h1 class="text-gray-800 text-3xl md:text-4xl font-bold flex items-center justify-center gap-3">
				<svg class="w-8 h-8 md:w-10 md:h-10 text-green-500 inline-block" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
					<circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
					<path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
				</svg>
				Payment Successful!
			</h1>

			<p class="text-gray-600 text-lg mt-8 leading-relaxed" style="text-align:center; padding:2rem;">
				Redirecting back to the app…
			</p>
		{:else}
			<!-- Web browser message -->
			<h1 class="text-gray-800 text-3xl md:text-4xl font-bold flex items-center justify-center gap-3">
				<svg class="w-8 h-8 md:w-10 md:h-10 text-green-500 inline-block" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
					<circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
					<path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
				</svg>
				Payment Successful!
			</h1>

			<p class="text-gray-600 text-lg mt-8 leading-relaxed">
				Your payment for <span class="font-bold">Your Best Days</span> app subscription has been completed successfully.
			</p>

			<div class="bg-gray-50 rounded-lg p-6 mb-8">
				<p class="text-gray-700">You can return to the <span class="font-bold">Your Best Days</span> app and enjoy all features now. The app will update your subscription automatically.</p>
			</div>

			<div class="flex gap-4 justify-center flex-wrap">
				<button on:click={closeWindow} class="inline-block px-6 py-3 rounded-md font-medium transition-all duration-200 bg-gray-200 text-gray-700 hover:bg-gray-300 cursor-pointer">
					Close Window
				</button>
			</div>

			{#if showCloseMessage}
				<div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
					<p class="text-sm text-blue-700">
						Please close this tab manually or return to the Your Best Days app.
					</p>
				</div>
			{/if}
		{/if}
		</div>
		
		<div class="bg-gray-50 rounded-b-xl py-3 px-8">
			<p class="text-xs">
				Your Best Days - brought to you by UpliftingActions LLC
			</p>
		</div>
	</div>
</div>

<style>
	/* Mobile responsive adjustments */
	@media (max-width: 640px) {
		.flex-wrap button {
			@apply w-full text-center;
		}
	}
</style>
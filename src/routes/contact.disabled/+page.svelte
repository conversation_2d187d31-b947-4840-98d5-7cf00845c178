<script lang="ts">
	import SocialIcons from '$lib/SocialIcons.svelte';
</script>

<svelte:head>
	<title>Contact - UpliftingActions</title>
	<meta name="description" content="Contact UpliftingActions - Get in touch with us on social media" />
</svelte:head>

<!-- Main Content -->
<main class="relative min-h-screen bg-gray-900 flex items-center justify-center overflow-hidden">
	<!-- Background Image Overlay -->
	<div class="absolute inset-0 bg-gradient-to-b from-gray-900/80 to-gray-900/60"></div>
	
	<!-- Background Pattern/Texture -->
	<div class="absolute inset-0 opacity-20">
		<div class="absolute inset-0 bg-gradient-to-br from-blue-900/30 to-transparent"></div>
		<!-- Simulated cityscape pattern -->
		<div class="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/50 to-transparent"></div>
	</div>
	
	<!-- Contact Content -->
	<div class="relative z-10 text-left px-6 max-w-4xl mx-auto">
		<h1 class="text-4xl md:text-5xl lg:text-6xl font-light text-white leading-tight mb-8">
			Contact
		</h1>
		
		<p class="text-lg md:text-xl text-gray-300 mb-12 leading-relaxed">
			Got questions or feedback? Contact us on social media. If the message is personal, send us a private message (DM).
		</p>
		
		<!-- Social Media Links -->
		<SocialIcons size="large" />
	</div>
</main>
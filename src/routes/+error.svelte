<script>
	import { page } from '$app/stores';
	import { base } from '$app/paths';

	$: status = $page.status;
	$: message = $page.error?.message || 'An error occurred';

	function goBack() {
		history.back();
	}
</script>

<svelte:head>
	<title>{status} - Uplifting Actions</title>
	<meta name="description" content="Error {status}: {message}" />
</svelte:head>

<div class="error-container">
	<div class="error-content">
		<h1 class="error-code">{status}</h1>
		
		{#if status === 404}
			<h2 class="error-title">Page Not Found</h2>
			<p class="error-message">
				Sorry, the page you're looking for doesn't exist or has been moved.
			</p>
		{:else if status === 500}
			<h2 class="error-title">Server Error</h2>
			<p class="error-message">
				Something went wrong on our end. Please try again later.
			</p>
		{:else}
			<h2 class="error-title">Error {status}</h2>
			<p class="error-message">{message}</p>
		{/if}

		<div class="error-actions">
			<a href="{base}/" class="btn-primary">
				← Back to Home
			</a>
			<button on:click={goBack} class="btn-secondary">
				Go Back
			</button>
		</div>
	</div>
</div>

<style>
	.error-container {
		min-height: 100vh;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 2rem;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
		text-align: center;
	}

	.error-content {
		max-width: 600px;
		width: 100%;
	}

	.error-code {
		font-size: 8rem;
		font-weight: 900;
		margin: 0;
		line-height: 1;
		text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
		background: linear-gradient(45deg, #fff, #f0f0f0);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		background-clip: text;
	}

	.error-title {
		font-size: 2.5rem;
		font-weight: 700;
		margin: 1rem 0;
		text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
	}

	.error-message {
		font-size: 1.2rem;
		margin: 1.5rem 0 2.5rem;
		opacity: 0.9;
		line-height: 1.6;
	}

	.error-actions {
		display: flex;
		gap: 1rem;
		justify-content: center;
		flex-wrap: wrap;
	}

	.btn-primary,
	.btn-secondary {
		padding: 0.75rem 1.5rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: all 0.3s ease;
		border: none;
		cursor: pointer;
		font-size: 1rem;
	}

	.btn-primary {
		background: rgba(255, 255, 255, 0.2);
		color: white;
		border: 2px solid rgba(255, 255, 255, 0.3);
	}

	.btn-primary:hover {
		background: rgba(255, 255, 255, 0.3);
		transform: translateY(-2px);
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
	}

	.btn-secondary {
		background: transparent;
		color: white;
		border: 2px solid rgba(255, 255, 255, 0.5);
	}

	.btn-secondary:hover {
		background: rgba(255, 255, 255, 0.1);
		transform: translateY(-2px);
	}

	@media (max-width: 768px) {
		.error-code {
			font-size: 6rem;
		}

		.error-title {
			font-size: 2rem;
		}

		.error-message {
			font-size: 1.1rem;
		}

		.error-actions {
			flex-direction: column;
			align-items: center;
		}

		.btn-primary,
		.btn-secondary {
			width: 100%;
			max-width: 200px;
		}
	}
</style>
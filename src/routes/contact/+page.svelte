<script lang="ts">
	import SocialIcons from '$lib/SocialIcons.svelte';
</script>

<svelte:head>
	<title>Contact - UpliftingActions</title>
	<meta name="description" content="Contact UpliftingActions - Get in touch with us on social media" />
</svelte:head>

<!-- Contact Page -->
<main class="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-gray-900 flex items-center justify-center px-5 relative overflow-hidden">
	<!-- Animated Background Elements -->
	<div class="absolute inset-0 opacity-10">
		<div class="absolute top-20 left-20 w-32 h-32 bg-blue-400 rounded-full blur-xl animate-pulse"></div>
		<div class="absolute top-40 right-32 w-24 h-24 bg-purple-400 rounded-full blur-xl animate-pulse delay-1000"></div>
		<div class="absolute bottom-32 left-1/3 w-40 h-40 bg-indigo-400 rounded-full blur-xl animate-pulse delay-2000"></div>
		<div class="absolute bottom-20 right-20 w-28 h-28 bg-cyan-400 rounded-full blur-xl animate-pulse delay-3000"></div>
	</div>
	
	<!-- Main Content -->
	<div class="relative z-10 text-center max-w-4xl mx-auto">
		<!-- Logo -->
		<div class="mb-12 animate-fade-in">
			<img src="/ua_logo.svg" alt="UpliftingActions Logo" class="h-20 md:h-32 mx-auto mb-6 drop-shadow-2xl" />
		</div>
		
		<!-- Contact Header -->
		<div class="mb-8 animate-slide-up">
			<h1 class="text-3xl md:text-4xl lg:text-6xl font-light text-white leading-tight mb-6">
				Contact Us
			</h1>
			
			<p class="text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed">
				Our website is currently undergoing maintenance.
				<br class="hidden md:block">
				For assistance, please reach out via social media DM.
			</p>
			
			<!-- Status Indicator -->
			<div class="inline-flex items-center bg-blue-900/30 backdrop-blur-sm rounded-full px-6 py-3 border border-blue-400/20 mb-6">
				<div class="w-3 h-3 bg-blue-400 rounded-full animate-pulse mr-3"></div>
				<span class="text-blue-200 font-medium">Site Under Maintenance</span>
			</div>
		</div>
		
		<!-- Contact Section -->
		<div class="mb-12 animate-fade-in-delayed">
			<h2 class="text-2xl font-light text-white mb-6">
				Need Help During Maintenance?
			</h2>

			<div class="flex justify-center items-center">
				<p class="text-gray-400 mb-8 text-lg max-w-2xl text-center">
					While our website is under maintenance, we're still here to help! Send us a <strong class="text-blue-300">direct message (DM)</strong> on any of our social media platforms below for assistance.
				</p>
			</div>
			
			<!-- Social Icons -->
			<div class="flex justify-center mt-4">
				<div class="bg-black/20 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
					<SocialIcons size="large" />
				</div>
			</div>
		</div>
		
		<!-- Additional Contact Info -->
		<div class="animate-fade-in-delayed-2">
			<div class="bg-blue-900/20 backdrop-blur-sm rounded-xl p-6 border border-blue-400/20 max-w-2xl mx-auto">
				<h3 class="text-xl font-light text-white mb-4">Follow Us On</h3>
				<div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-gray-300">
					<div class="text-center">
						<p class="font-medium text-blue-400">YouTube</p>
						<p class="text-sm">@upliftingactions</p>
					</div>
					<div class="text-center">
						<p class="font-medium text-blue-400">Twitter</p>
						<p class="text-sm">@UpliftingAction</p>
					</div>
					<div class="text-center">
						<p class="font-medium text-blue-400">Instagram</p>
						<p class="text-sm">@upliftingactions</p>
					</div>
					<div class="text-center">
						<p class="font-medium text-blue-400">Facebook</p>
						<p class="text-sm">UpliftingActionsLLC</p>
					</div>
				</div>
			</div>
		</div>
	</div>
	
	<!-- Decorative Elements -->
	<div class="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/30 to-transparent"></div>
</main>

<style>
	@import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;500;600;700&display=swap');
	
	:global(.font-script) {
		font-family: 'Dancing Script', cursive;
	}
	
	/* Animations */
	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(20px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
	
	@keyframes slideUp {
		from {
			opacity: 0;
			transform: translateY(40px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
	
	.animate-fade-in {
		animation: fadeIn 1s ease-out;
	}
	
	.animate-slide-up {
		animation: slideUp 1s ease-out 0.3s both;
	}
	
	.animate-fade-in-delayed {
		animation: fadeIn 1s ease-out 0.6s both;
	}
	
	.animate-fade-in-delayed-2 {
		animation: fadeIn 1s ease-out 0.9s both;
	}
	
	/* Pulse animation for background elements */
	@keyframes pulse {
		0%, 100% {
			transform: scale(1);
			opacity: 0.5;
		}
		50% {
			transform: scale(1.3);
			opacity: 0.8;
		}
	}
	
	.animate-pulse {
		animation: pulse 1.5s ease-in-out infinite;
	}
	
	.delay-1000 {
		animation-delay: 1s;
	}
	
	.delay-2000 {
		animation-delay: 2s;
	}
	
	.delay-3000 {
		animation-delay: 3s;
	}
</style>
<script lang="ts">
	import SocialIcons from '$lib/SocialIcons.svelte';
</script>

<svelte:head>
	<title>UpliftingActions - Site Under Maintenance</title>
	<meta name="description" content="UpliftingActions is currently undergoing maintenance. We'll be back soon!" />
</svelte:head>

<!-- Maintenance Page -->
<main class="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-gray-900 flex items-start justify-center pt-16 px-5 relative overflow-hidden">
	<!-- Animated Background Elements -->
	<div class="absolute inset-0 opacity-10">
		<div class="absolute top-20 left-20 w-32 h-32 bg-blue-400 rounded-full blur-xl animate-pulse"></div>
		<div class="absolute top-40 right-32 w-24 h-24 bg-purple-400 rounded-full blur-xl animate-pulse delay-1000"></div>
		<div class="absolute bottom-32 left-1/3 w-40 h-40 bg-indigo-400 rounded-full blur-xl animate-pulse delay-2000"></div>
		<div class="absolute bottom-20 right-20 w-28 h-28 bg-cyan-400 rounded-full blur-xl animate-pulse delay-3000"></div>
	</div>
	
	<!-- Main Content -->
	<div class="relative z-10 text-center max-w-4xl mx-auto">
		<!-- Logo -->
		<div class="mb-12 animate-fade-in">
			<img src="/ua_logo.svg" alt="UpliftingActions Logo" class="h-20 md:h-32 mx-auto mb-6 drop-shadow-2xl" />
		</div>
		
		<!-- Maintenance Message -->
		<div class="mb-6 animate-slide-up">
			<h1 class="text-2xl md:text-3xl lg:text-5xl font-light text-white leading-tight mb-6">
				We're Making Things
				<br>
				<span class="text-blue-400 font-script text-3xl md:text-5xl lg:text-6xl italic">Better</span>
			</h1>
			
			<p class="text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed">
				Our website is currently undergoing maintenance to serve you better.
				<br class="hidden md:block">
				We'll be back online soon with improvements!
			</p>
			
			<!-- Status Indicator -->
			<div class="inline-flex items-center bg-blue-900/30 backdrop-blur-sm rounded-full px-6 py-3 border border-blue-400/20">
				<div class="w-3 h-3 bg-blue-400 rounded-full animate-pulse mr-3"></div>
				<span class="text-blue-200 font-medium">Maintenance in Progress</span>
			</div>
		</div>
		
		<!-- Contact Section -->
		<div class="mb-12 animate-fade-in-delayed">
			<h2 class="text-2xl font-light text-white">
				Stay Connected
			</h2>

			<div class="flex justify-center items-center">
				<p class="text-gray-400 mb-8 text-lg max-w-96">
					Got questions or feedback? Contact us on social media. If the message is personal, send us a private message (DM).
				</p>
			</div>
			
			<!-- Social Icons -->
			<div class="flex justify-center mt-4">
				<div class="bg-black/20 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
					<SocialIcons size="large" />
				</div>
			</div>
		</div>
		
		<!-- Expected Return -->
		<div class="animate-fade-in-delayed-2">
			<p class="text-gray-500 text-sm md:text-base">
				Thank you for your patience!
			</p>
		</div>
	</div>
	
	<!-- Decorative Elements -->
	<div class="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/30 to-transparent"></div>
</main>

<style>
	@import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;500;600;700&display=swap');
	
	:global(.font-script) {
		font-family: 'Dancing Script', cursive;
	}
	
	/* Animations */
	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(20px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
	
	@keyframes slideUp {
		from {
			opacity: 0;
			transform: translateY(40px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
	
	.animate-fade-in {
		animation: fadeIn 1s ease-out;
	}
	
	.animate-slide-up {
		animation: slideUp 1s ease-out 0.3s both;
	}
	
	.animate-fade-in-delayed {
		animation: fadeIn 1s ease-out 0.6s both;
	}
	
	.animate-fade-in-delayed-2 {
		animation: fadeIn 1s ease-out 0.9s both;
	}
	
	/* Pulse animation for background elements */
	@keyframes pulse {
		0%, 100% {
			transform: scale(1);
			opacity: 0.5;
		}
		50% {
			transform: scale(1.3);
			opacity: 0.8;
		}
	}
	
	.animate-pulse {
		animation: pulse 1.5s ease-in-out infinite;
	}
	
	.delay-1000 {
		animation-delay: 1s;
	}
	
	.delay-2000 {
		animation-delay: 2s;
	}
	
	.delay-3000 {
		animation-delay: 3s;
	}
</style>
<script lang="ts">
	import '../app.css';
	
	let { children } = $props();
</script>

<!-- Simplified Layout for Maintenance Mode -->
<div class="min-h-screen flex flex-col">
	<!-- Page Content -->
	{@render children()}
</div>

<style>
	@import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;500;600;700&display=swap');
	
	:global(.font-script) {
		font-family: 'Dancing Script', cursive;
	}
	
	/* Global styles for maintenance mode */
	:global(body) {
		margin: 0;
		padding: 0;
		overflow-x: hidden;
	}
	
	:global(*) {
		box-sizing: border-box;
	}
</style>
